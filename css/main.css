:root {
  --primary-color: #1585cf;
  --text-color: #000;
  --bg-color: #fff;
  --border-color: #ddd;
  --font-family: "Open Sans", Arial, sans-serif;
  --font-size-base: 1.05rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 3rem;
}

[data-theme="dark"] {
  --text-color: #f0f0f0;
  --bg-color: #121212;
  --border-color: #333;
}

* {
  box-sizing: border-box;
}

body {
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  background: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family);
  color: var(--text-color);
  font-weight: 500;
  margin: 0 0 var(--spacing-md);
}

p, table, tr, td, ul, li, blockquote, nav, a, footer, dl, dt, dd {
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  font-weight: 400;
}

p {
  line-height: 1.5;
  margin-bottom: 0.9rem;
  text-align: justify;
}

#main-container a {
  color: var(--primary-color);
  font-weight: 500;
  transition: color 0.2s ease;
}

#main-container a:hover {
  color: #0f6ba8;
  text-decoration: none;
}

.main-title {
  margin-top: var(--spacing-lg);
  
  text-align: left;
}

.footer-controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
}

.dark-mode-toggle {
  background: none;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  cursor: pointer;
  color: var(--text-color);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base); 
  flex-shrink: 0;
}

.dark-mode-toggle:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.dark-mode-toggle:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.profile-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-right: var(--spacing-md);
}

.headshot-image {
  width: 100%;
  max-width: 250px;
  height: auto;
  border-radius: 8px;
  border: 3px solid var(--border-color);
  margin-bottom: var(--spacing-md);
  transition: border-color 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.contact-table {
  width: 100%;
  max-width: 250px;
  border: none;
  margin: 0;
}

.contact-table td {
  border: none;
  padding: 0.5rem 0;
  text-align: left;
  vertical-align: top;
}

.contact-table td:first-child {
  width: 30px;
  text-align: center;
  color: var(--text-color);
  padding-right: 0.75rem;
}

h1, h2, h3 {
  position: relative;
}

h1 span, h2 span, h3 span {
  background-color: var(--bg-color);
  padding-right: 10px;
  transition: background-color 0.3s ease;
}

h1:after, h2:after, h3:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 0.5em;
  border-top: 2px solid var(--text-color);
  z-index: -1;
  transition: border-color 0.3s ease;
}

hr {
  border-color: var(--border-color);
}

footer .row {
  display: flex;
  align-items: center;
  min-height: 40px;
}

footer p {
  margin: 0;
  line-height: 1.4;
}

@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    --text-color: #f0f0f0;
    --bg-color: #121212;
    --border-color: #333;
  }
}

@media (max-width: 767px) {
  .main-title {
    margin-top: var(--spacing-md);
    font-size: 2rem;
    text-align: left;
  }

  .profile-section {
    margin-bottom: var(--spacing-md);
    padding-right: 0;
  }

  .headshot-image {
    max-width: 200px;
  }

  .contact-table {
    max-width: 200px;
  }

  .contact-table td {
    padding: 0.3rem 0;
  }

  .contact-table td:first-child {
    padding-right: 0.5rem;
  }

  p, a, td {
    font-size: var(--font-size-base);
    text-align: left;
  }

  footer .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: nowrap;
  }

  footer .col-sm-6 {
    flex: 1;
    margin-bottom: 0;
  }

  footer .col-sm-6:first-child {
    margin-bottom: 0;
  }

  .footer-controls {
    justify-content: flex-end;
    margin-top: 0;
  }

  .dark-mode-toggle {
    width: 32px;
    height: 32px;
    font-size: var(--font-size-base);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 95%;
  }

  .profile-section {
    align-items: flex-start;
    padding-right: var(--spacing-md);
  }

  .headshot-image,
  .contact-table {
    max-width: 100%;
  }

  .col-sm-9 {
    padding-left: var(--spacing-md);
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .profile-section {
    padding-right: calc(var(--spacing-md) + 1rem);
  }

  .col-sm-9 {
    padding-left: calc(var(--spacing-md) + 1rem);
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 90%;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 85%;
  }
}

@media (min-width: 1400px) {
  .container {
    max-width: 80%;
  }
}